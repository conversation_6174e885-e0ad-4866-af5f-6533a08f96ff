"""
统一路由系统核心模块
实现PRD-20241220中定义的两级路由架构：
- 第一级：触发词识别（必须，确定生成管道）
- 第二级：管道内工作流选择（智能分析具体工作流）

同时提供统一的LLM分析服务，避免重复代码
"""

import time
import json
import logging
import re
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

from ..session.models import WorkflowType
from ...provider import entities as llm_entities
from ..intent.parameter_parser import parameter_parser, ParsedParameters


class RoutingLevel(Enum):
    """路由级别"""
    LEVEL_1 = 1  # 第一级：触发词识别
    LEVEL_2 = 2  # 第二级：管道内工作流选择


class RoutingConfidence(Enum):
    """路由置信度"""
    HIGH = "high"
    MEDIUM = "medium" 
    LOW = "low"
    UNKNOWN = "unknown"


class WorkflowSubType(Enum):
    """工作流子类型"""
    # AIGEN管道
    AIGEN_TEXT_ONLY = "aigen_text_only"           # 纯文生图
    AIGEN_CONTROL_ONLY = "aigen_control_only"     # 控制图
    AIGEN_REFERENCE_ONLY = "aigen_reference_only" # 参考图
    AIGEN_CONTROL_REFERENCE = "aigen_control_reference" # 控制+参考

    # KONTEXT管道 - 按具体图片数量区分
    KONTEXT_1IMAGE = "kontext_1image"             # 1张图片处理
    KONTEXT_2IMAGE = "kontext_2image"             # 2张图片处理
    KONTEXT_3IMAGE = "kontext_3image"             # 3张图片处理

    # KONTEXT_API管道 - 按具体图片数量区分
    KONTEXT_API_1IMAGE = "kontext_api_1image"     # 1张图片API
    KONTEXT_API_2IMAGE = "kontext_api_2image"     # 2张图片API
    KONTEXT_API_3IMAGE = "kontext_api_3image"     # 3张图片API


@dataclass
class ParameterAnalysisResult:
    """参数分析结果"""
    success: bool
    parameters: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0
    llm_used: bool = False
    fallback_used: bool = False
    error_message: str = ""
    analysis_time: float = 0.0


@dataclass
class IntentAnalysisResult:
    """意图分析结果"""
    success: bool
    image_types: Dict[int, str] = field(default_factory=dict)  # 图片索引 -> 类型
    confidence: float = 0.0
    reasoning: str = ""
    llm_used: bool = False
    fallback_used: bool = False
    error_message: str = ""
    analysis_time: float = 0.0


@dataclass
class _Level2RoutingResult:
    """第二级路由结果"""
    workflow_subtype: WorkflowSubType
    confidence: RoutingConfidence
    reasoning: str
    processing_time_ms: float
    fallback_used: bool = False
    suggested_prompt: str = ""
    needs_clarification: bool = False
    clarification_question: str = ""
    workflow_file: str = ""


@dataclass
class UnifiedRoutingResult:
    """统一路由结果"""
    routing_level: RoutingLevel
    workflow_type: WorkflowType
    workflow_subtype: WorkflowSubType
    confidence: RoutingConfidence
    reasoning: str
    processing_time_ms: float
    fallback_used: bool = False
    suggested_prompt: str = ""
    needs_clarification: bool = False
    clarification_question: str = ""
    workflow_file: str = ""  # 具体的工作流文件名


class UnifiedRoutingSystem:
    """统一路由系统 - 提供路由决策和LLM分析服务"""
    
    def __init__(self, ap=None):
        self.ap = ap
        self.logger = logging.getLogger(__name__)
        
        # 第一级路由配置（触发词识别）
        self.level_1_keywords = {
            "aigen": WorkflowType.AIGEN,
            "kontext": WorkflowType.KONTEXT,
            "kontext_api": WorkflowType.KONTEXT_API
        }

        # 第二级路由配置（管道内工作流选择）
        self.level_2_config = {
            "enabled": True,
            "timeout_ms": 2000,
            "model_name": "default",
            "system_prompt": self._get_routing_system_prompt()
        }
        
        # 工作流文件映射
        self.workflow_files = {
            WorkflowSubType.AIGEN_TEXT_ONLY: "flux_default.json",
            WorkflowSubType.AIGEN_CONTROL_ONLY: "flux_controlnet.json",
            WorkflowSubType.AIGEN_REFERENCE_ONLY: "flux_redux.json",
            WorkflowSubType.AIGEN_CONTROL_REFERENCE: "flux_controlnet_redux.json",
            WorkflowSubType.KONTEXT_1IMAGE: "kontext_local_single_image.json",
            WorkflowSubType.KONTEXT_2IMAGE: "kontext_local_double_images.json",
            WorkflowSubType.KONTEXT_3IMAGE: "kontext_local_triple_images.json",
            WorkflowSubType.KONTEXT_API_1IMAGE: "kontext_api_1image.json",
            WorkflowSubType.KONTEXT_API_2IMAGE: "kontext_api_2images.json",
            WorkflowSubType.KONTEXT_API_3IMAGE: "kontext_api_3images.json"
        }
        
        # 监控配置
        self.monitoring_config = {
            "enabled": True,
            "log_level": "INFO",
            "metrics_collection": True,
            "performance_tracking": True,
            "llm_timeout_ms": 5000  # LLM路由超时时间
        }

    def _get_routing_system_prompt(self) -> str:
        """获取路由系统提示词"""
        return """你是一个智能工作流路由器，负责理解用户意图并选择最合适的AI工作流。

## 可用的工作流类型：

### 1. aigen - 文生图工作流（本地Flux）
- **适用于**: 从文本描述生成图片，可选择性使用图片作为控制或参考
- **输入**: 文本提示词 + 可选图片（控制图/参考图）
- **特点**: 支持ControlNet、Redux、LoRA、高质量生成

### 2. kontext - 图生图工作流（本地Kontext）
- **适用于**: 基于现有图片进行编辑、修改、风格转换
- **输入**: 必需至少1张图片 + 编辑指令
- **特点**: 保持原图结构，精准编辑

### 3. kontext_api - 远程API工作流
- **适用于**: 复杂的图像处理，当本地资源不足时
- **输入**: 必需图片 + 处理指令
- **特点**: 云端处理，功能丰富

## 路由示例：

**示例1 - 选择aigen:**
用户: "画一只可爱的小猫，蓝色眼睛，坐在花园里"
分析: 纯文本描述生成图片 → aigen

**示例2 - 选择aigen (带参考图):**
用户: "参考这张图片的风格，画一个机器人" + 1张图片
分析: 文生图但需要参考图片风格 → aigen

**示例3 - 选择aigen (带控制图):**
用户: "按照这个姿势画一个女孩" + 1张人物姿势图
分析: 文生图但需要控制结构 → aigen

**示例4 - 选择kontext:**
用户: "把这张照片的背景改成海滩" + 1张照片
分析: 基于现有图片进行编辑 → kontext

**示例5 - 选择kontext_api:**
用户: "对这些图片进行专业级的批量处理" + 多张图片
分析: 复杂处理需要云端资源 → kontext_api

## 判断原则：
1. **创作意图**: 是"生成新图片"还是"编辑现有图片"？
2. **图片角色**: 图片是"辅助生成"还是"编辑对象"？
3. **处理复杂度**: 是否需要云端资源？

请以JSON格式返回结果：
{
  "workflow_type": "aigen|kontext|kontext_api",
  "confidence": "high|medium|low",
  "reasoning": "选择原因",
  "suggested_prompt": "建议的提示词"
}
"""

    def _get_aigen_workflow_system_prompt(self) -> str:
        """获取AIGEN管道内工作流选择的系统提示词"""
        return """你是AIGEN图像生成工作流的智能路由器，负责根据用户意图和输入条件选择最合适的具体工作流。

## 可用的AIGEN工作流类型：

### 1. TEXT_ONLY - 纯文生图工作流
- **文件**: flux_default.json
- **适用场景**: 仅有文本描述，无图片输入
- **功能**: 根据文本提示词生成全新图像

### 2. CONTROL_ONLY - ControlNet控制工作流
- **文件**: flux_controlnet.json
- **适用场景**: 需要控制图像的结构、姿势、轮廓、构图
- **ControlNet作用**:
  - 控制图像的骨架结构和布局
  - 保持特定的姿势和形状
  - 从线稿、深度图、边缘图等提取结构信息
  - 确保生成图像遵循特定的构图和几何形状

### 3. REFERENCE_ONLY - Redux参考工作流
- **文件**: flux_redux.json
- **适用场景**: 需要参考图像的风格、色彩、氛围
- **Redux作用**:
  - 从参考图中提取风格特征
  - 保持参考图的色调和氛围
  - 进行风格迁移和视觉特征复制
  - 生成具有相似视觉风格的新图像

### 4. CONTROL_REFERENCE - 混合工作流
- **文件**: flux_controlnet_redux.json
- **适用场景**: 同时需要结构控制和风格参考
- **功能**: 结合ControlNet的结构控制和Redux的风格参考

## 判断规则与示例：

### 示例1 - TEXT_ONLY (无图片)
**用户输入**: "画一只可爱的橘猫，坐在阳台上，背景是夕阳"
**图片数量**: 0
**分析**: 纯文本描述，无图片输入
**选择**: TEXT_ONLY

### 示例2 - CONTROL_ONLY (明确指定控制图)
**用户输入**: "以这张图片为控制图，生成一个科幻战士" + 1张姿势图
**图片数量**: 1
**分析**: 用户明确说"以这张图片为控制图"，意图非常清晰
**选择**: CONTROL_ONLY

### 示例3 - REFERENCE_ONLY (明确指定参考图)
**用户输入**: "以这张图为参考图，生成一个类似风格的城市" + 1张艺术作品
**图片数量**: 1
**分析**: 用户明确说"以这张图为参考图"，意图非常清晰
**选择**: REFERENCE_ONLY

### 示例4 - CONTROL_ONLY (隐含控制意图)
**用户输入**: "按照这个姿势画一个女孩，要动漫风格" + 1张人物姿势图
**图片数量**: 1
**分析**: "按照这个姿势"表明要控制人物结构和构图
**选择**: CONTROL_ONLY

### 示例5 - REFERENCE_ONLY (隐含参考意图)
**用户输入**: "参考这张图的色彩和风格，画一个机器人" + 1张艺术作品
**图片数量**: 1
**分析**: "参考色彩和风格"表明要模仿视觉特征，不是结构控制
**选择**: REFERENCE_ONLY

### 示例4 - CONTROL_REFERENCE (混合使用)
**用户输入**: "第一张图控制姿势，第二张图参考风格，画一个战士" + 2张图片
**图片数量**: 2
**分析**: 明确指定了两张图片的不同用途
**选择**: CONTROL_REFERENCE

### 示例5 - 模糊情况的判断
**用户输入**: "根据这张图画一个类似的角色" + 1张动漫角色图
**图片数量**: 1
**分析**: "类似的角色"更偏向风格和特征参考，而非结构控制
**选择**: REFERENCE_ONLY

## 关键判断原则：
1. **控制 vs 参考**:
   - 控制 = 结构、姿势、构图、轮廓、布局
   - 参考 = 风格、色彩、氛围、质感、艺术效果
2. **用户意图**: 理解用户真实想要什么，而非关键词匹配
3. **图片角色**: 每张图片在生成过程中的具体作用

请以JSON格式返回结果：
{
  "workflow_subtype": "TEXT_ONLY|CONTROL_ONLY|REFERENCE_ONLY|CONTROL_REFERENCE",
  "confidence": "high|medium|low",
  "reasoning": "详细的选择原因",
  "suggested_prompt": "优化后的英文提示词",
  "image_analysis": {
    "image_1_type": "control|reference|unknown",
    "image_2_type": "control|reference|unknown",
    "usage_explanation": "图片用途说明"
  }
}
"""

    def _get_parameter_analysis_prompt(self) -> str:
        """获取参数分析系统提示词"""
        return """You are an expert AI image generation parameter analyzer. Your task is to analyze user requests and provide optimal parameters for Flux/ComfyUI image generation.

Analyze the user request and return a JSON object with the following parameters:

1. **prompt**: Enhanced English prompt (natural language, not keywords)
2. **width**: Image width (choose from: 1024 for square; 1280, 1440 for landscape; 800, 720 for portrait)
3. **height**: Image height (choose from: 1024 for square; 800, 720 for landscape; 1280, 1440 for portrait)
4. **steps**: Sampling steps (15-30, default 20)
5. **guidance**: CFG guidance (2.0-5.0, default 3.5)
6. **aspect_ratio**: Detected aspect ratio ("landscape", "portrait", "square")
7. **seed_instruction**: Seed handling instruction ("random", "use_last", "specific:<number>")

**Aspect Ratio Detection Rules:**
- 横版/宽屏/横向/landscape → width > height (e.g., 1280x800, 1440x720)
- 竖版/竖向/portrait → height > width (e.g., 800x1280, 720x1440)  
- 正方形/方形/square → width = height (1024x1024)
- No specific mention → default square (1024x1024)

**Resolution Strategy (Target ~1M pixels):**
- Square: 1024x1024 (1,048,576 pixels)
- Landscape: 1280x800 (1,024,000 pixels) or 1440x720 (1,036,800 pixels)
- Portrait: 800x1280 (1,024,000 pixels) or 720x1440 (1,036,800 pixels)

**Seed Handling Rules:**
- Default behavior → "random" (generate new random seed each time)
- 使用上一次种子/用上次的/same seed/previous seed → "use_last"
- 种子123/seed 456/固定种子 → "specific:<number>"
- No mention of seed → "random"

**Quality Keywords → Steps/Guidance:**
- 高质量/精细/详细 → steps: 25-30, guidance: 4.0
- 快速/简单 → steps: 15-20, guidance: 3.0
- Default → steps: 20, guidance: 3.5

Now analyze this user request and return ONLY a valid JSON object:"""



    async def _get_llm_model(self, query) -> Optional[Any]:
        """获取LLM模型"""
        if not self.ap:
            self.logger.warning("应用实例未设置，无法获取LLM模型")
            return None

        if not query or not hasattr(query, 'pipeline_config') or not query.pipeline_config:
            self.logger.warning("查询对象或pipeline_config未设置，无法获取LLM模型")
            return None

        # 获取LLM模型配置
        ai_config = query.pipeline_config.get('ai', {})
        if not ai_config:
            self.logger.warning("pipeline_config中未找到ai配置")
            return None

        local_agent_config = ai_config.get('local-agent', {})
        if not local_agent_config:
            self.logger.warning("ai配置中未找到local-agent配置")
            return None

        model_uuid = local_agent_config.get('model', '')
        if not model_uuid:
            self.logger.warning("local-agent配置中未找到model UUID")
            return None

        # 找到对应的RuntimeLLMModel
        runtime_llm_model = None
        available_models = []
        for model in self.ap.model_mgr.llm_models:
            available_models.append(model.model_entity.uuid)
            if model.model_entity.uuid == model_uuid:
                runtime_llm_model = model
                break

        if not runtime_llm_model:
            self.logger.warning(f"未找到模型 {model_uuid}，可用模型: {available_models}")
            return None

        self.logger.info(f"成功获取LLM模型: {model_uuid}")
        return runtime_llm_model

    async def _call_llm(self, query, system_prompt: str, user_prompt: str) -> Optional[str]:
        """统一的LLM调用接口"""
        try:
            runtime_llm_model = await self._get_llm_model(query)
            if not runtime_llm_model:
                self.logger.warning("无法获取LLM模型，LLM调用失败")
                return None
            
            # 创建消息
            messages = [
                llm_entities.Message(role='system', content=system_prompt),
                llm_entities.Message(role='user', content=user_prompt)
            ]
            
            # 调用LLM
            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],  # 不需要工具调用
                extra_args={},
            )
            
            # 提取响应文本
            response_text = ""
            if hasattr(result, 'content') and result.content:
                if isinstance(result.content, list):
                    for element in result.content:
                        if hasattr(element, 'text') and element.text:
                            response_text += element.text
                elif isinstance(result.content, str):
                    response_text = result.content
                else:
                    response_text = str(result.content)
            
            self.logger.info(f"LLM调用成功，响应长度: {len(response_text)}")
            return response_text.strip()

        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            import traceback
            self.logger.error(f"LLM调用错误详情: {traceback.format_exc()}")
            return None

    def _clean_json_response(self, response_text: str) -> str:
        """清理LLM返回的JSON响应"""
        if not response_text:
            return ""
        
        # 清理markdown代码块标记
        cleaned_text = response_text
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]
        if cleaned_text.startswith('```'):
            cleaned_text = cleaned_text[3:]
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]
        
        return cleaned_text.strip()

    async def analyze_parameters(self, user_text: str, query) -> ParameterAnalysisResult:
        """统一的参数分析服务"""
        start_time = time.time()
        result = ParameterAnalysisResult(success=False)
        
        try:
            # 尝试使用LLM分析
            system_prompt = self._get_parameter_analysis_prompt()
            user_prompt = f"User request: {user_text}"
            
            response_text = await self._call_llm(query, system_prompt, user_prompt)
            
            if response_text:
                result.llm_used = True
                self.logger.info(f"LLM参数分析响应: {response_text}")
                
                try:
                    # 清理和解析JSON
                    cleaned_text = self._clean_json_response(response_text)
                    params_json = json.loads(cleaned_text)
                    
                    # 验证和补充参数
                    validated_params = self._validate_and_complete_params(params_json, user_text)
                    
                    result.success = True
                    result.parameters = validated_params
                    result.confidence = 0.9
                    
                    self.logger.info(f"参数分析成功: {validated_params}")
                    
                except json.JSONDecodeError as e:
                    self.logger.warning(f"LLM返回的JSON格式错误: {e}")
                    # 尝试提取JSON部分（备用方案）
                    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                    if json_match:
                        try:
                            params_json = json.loads(json_match.group())
                            validated_params = self._validate_and_complete_params(params_json, user_text)
                            result.success = True
                            result.parameters = validated_params
                            result.confidence = 0.7
                            self.logger.info(f"提取JSON成功: {validated_params}")
                        except:
                            pass
            
            # 如果LLM分析失败，使用默认参数
            if not result.success:
                result.fallback_used = True
                result.parameters = self._get_default_params(user_text)
                result.confidence = 0.5
                result.error_message = "LLM分析失败，使用默认参数"
                
        except Exception as e:
            self.logger.error(f"参数分析失败: {e}")
            result.fallback_used = True
            result.parameters = self._get_default_params(user_text)
            result.confidence = 0.5
            result.error_message = f"分析异常: {str(e)}"
        
        finally:
            result.analysis_time = time.time() - start_time
        
        return result

    async def analyze_intent(self, user_text: str, image_count: int, query) -> IntentAnalysisResult:
        """统一的意图分析服务"""
        start_time = time.time()
        result = IntentAnalysisResult(success=False)
        
        try:
            if image_count == 0:
                result.success = True
                result.image_types = {}
                result.confidence = 1.0
                result.reasoning = "没有图片"
                return result
            
            # 使用AIGEN工作流的LLM分析（复用现有逻辑）
            system_prompt = self._get_aigen_workflow_system_prompt()
            user_prompt = f"""用户输入: {user_text}
图片数量: {image_count}
有图片: {'是' if image_count > 0 else '否'}

请分析用户意图，判断每张图片的用途是什么？"""
            
            response_text = await self._call_llm(query, system_prompt, user_prompt)
            
            if response_text:
                result.llm_used = True
                self.logger.info(f"LLM意图分析响应: {response_text}")
                
                try:
                    # 清理和解析JSON
                    cleaned_text = self._clean_json_response(response_text)
                    data = json.loads(cleaned_text)
                    
                    # 验证结果
                    if ('image_types' in data and 
                        'confidence' in data and 
                        'reasoning' in data):
                        
                        # 转换并验证图片类型
                        image_types = {}
                        for idx_str, img_type in data['image_types'].items():
                            idx = int(idx_str)
                            if 0 <= idx < image_count and img_type in ['control', 'reference', 'mixed', 'unknown']:
                                image_types[idx] = img_type
                        
                        result.success = True
                        result.image_types = image_types
                        result.confidence = float(data['confidence'])
                        result.reasoning = str(data['reasoning'])
                        
                        self.logger.info(f"意图分析成功: {result.reasoning}")
                        
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    self.logger.warning(f"解析LLM响应失败: {e}")
            
            # 如果LLM分析失败，诚实告知用户
            if not result.success:
                result.fallback_used = True
                result.success = False  # 保持失败状态
                result.error_message = "LLM意图分析暂时不可用，请使用更明确的描述，如'以这张图片为控制图'或'参考这张图片的风格'"

        except Exception as e:
            self.logger.error(f"意图分析失败: {e}")
            result.fallback_used = True
            result.success = False  # 保持失败状态
            result.error_message = f"LLM意图分析异常: {str(e)}，请使用更明确的描述"
        
        finally:
            result.analysis_time = time.time() - start_time
        
        return result

    def _validate_and_complete_params(self, params_json: Dict[str, Any], user_text: str) -> Dict[str, Any]:
        """验证和补充参数"""
        # 基本参数验证和补充
        params = {
            'prompt': params_json.get('prompt', user_text),
            'width': params_json.get('width', 1024),
            'height': params_json.get('height', 1024),
            'steps': params_json.get('steps', 20),
            'guidance': params_json.get('guidance', 3.5),
            'aspect_ratio': params_json.get('aspect_ratio', 'square'),
            'seed_instruction': params_json.get('seed_instruction', 'random')
        }
        
        # 验证数值范围
        params['width'] = max(512, min(2048, params['width']))
        params['height'] = max(512, min(2048, params['height']))
        params['steps'] = max(10, min(50, params['steps']))
        params['guidance'] = max(1.0, min(10.0, params['guidance']))
        
        return params

    def _get_default_params(self, user_text: str) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            'prompt': user_text,
            'width': 1024,
            'height': 1024,
            'steps': 20,
            'guidance': 3.5,
            'aspect_ratio': 'square',
            'seed_instruction': 'random'
        }



    async def route_unified(
        self,
        user_text: str,
        has_images: bool = False,
        image_count: int = 0,
        query: Optional[Any] = None
    ) -> Optional[UnifiedRoutingResult]:
        """
        统一路由入口

        执行逻辑:
        1. 第一级：检查触发词，确定生成管道
        2. 第二级：在管道内选择具体工作流
        3. 如果没有触发词，返回None（普通聊天）
        """
        start_time = time.time()

        try:
            # 首先解析用户输入中的参数
            parsed_params = parameter_parser.parse_user_input(user_text)
            clean_user_text = parsed_params.clean_prompt

            self.logger.info(f"🔍 路由分析开始 - 原始输入: '{user_text}', 清理后: '{clean_user_text}', 图片: {has_images}, 数量: {image_count}")
            if parsed_params.use_civitai:
                self.logger.info(f"🌐 检测到Civitai参数: {parsed_params.civitai_query}")

            # 第一级路由：触发词识别（必须）
            level_1_result = self._route_level_1(user_text)  # 使用原始输入检查触发词
            self.logger.info(f"🎯 第一级路由结果: {level_1_result}")

            if not level_1_result:
                # 没有触发词，是普通聊天
                self.logger.info("未检测到触发词，视为普通聊天")
                return None

            # 第二级路由：管道内工作流选择
            self.logger.info(f"🔄 开始第二级路由 - 管道类型: {level_1_result.value}")
            level_2_result = await self._route_level_2(
                level_1_result, clean_user_text, has_images, image_count, query  # 使用清理后的文本
            )
            self.logger.info(f"✅ 第二级路由完成 - 工作流: {level_2_result.workflow_subtype.value}")

            processing_time = (time.time() - start_time) * 1000

            final_result = UnifiedRoutingResult(
                routing_level=RoutingLevel.LEVEL_2,
                workflow_type=level_1_result,
                workflow_subtype=level_2_result.workflow_subtype,
                confidence=level_2_result.confidence,
                reasoning=level_2_result.reasoning,
                processing_time_ms=processing_time,
                fallback_used=level_2_result.fallback_used,
                suggested_prompt=level_2_result.suggested_prompt,
                needs_clarification=level_2_result.needs_clarification,
                clarification_question=level_2_result.clarification_question,
                workflow_file=level_2_result.workflow_file
            )

            # 添加解析的参数信息
            if parsed_params.raw_parameters:
                final_result.metadata = final_result.metadata or {}
                final_result.metadata['parsed_parameters'] = parsed_params.raw_parameters
                final_result.metadata['use_civitai'] = parsed_params.use_civitai
                final_result.metadata['civitai_query'] = parsed_params.civitai_query
                final_result.metadata['clean_prompt'] = clean_user_text

            self.logger.info(f"🎉 路由分析成功 - 最终建议提示词: '{final_result.suggested_prompt}'")
            return final_result

        except Exception as e:
            self.logger.error(f"❌ 统一路由失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None

    def _route_level_1(self, user_text: str) -> Optional[WorkflowType]:
        """
        第一级触发词路由

        Args:
            user_text: 用户输入文本

        Returns:
            WorkflowType: 匹配的管道类型，None表示无触发词（普通聊天）

        性能要求: <5ms
        可靠性要求: 99.99%
        """
        if not user_text:
            return None

        user_text_lower = user_text.lower().strip()

        # 检查精确触发词匹配
        for keyword, workflow_type in self.level_1_keywords.items():
            if user_text_lower.startswith(keyword.lower() + " "):
                self.logger.info(f"第一级路由匹配: {keyword} -> {workflow_type.value}")
                return workflow_type

        return None







    async def _route_level_2(
        self,
        pipeline_type: WorkflowType,
        user_text: str,
        has_images: bool = False,
        image_count: int = 0,
        query: Optional[Any] = None
    ) -> '_Level2RoutingResult':
        """
        第二级管道内工作流选择
        
        Args:
            pipeline_type: 已确定的管道类型
            user_text: 用户输入文本
            has_images: 是否有图片
            image_count: 图片数量
            query: 查询对象（用于LLM调用）
        
        Returns:
            _Level2RoutingResult: 详细的路由结果
        """
        start_time = time.time()
        
        # 根据管道类型选择不同的处理逻辑
        if pipeline_type == WorkflowType.AIGEN:
            result = await self._route_aigen_pipeline(user_text, has_images, image_count, query)
        elif pipeline_type == WorkflowType.KONTEXT:
            result = self._route_kontext_pipeline(has_images, image_count)
        elif pipeline_type == WorkflowType.KONTEXT_API:
            result = await self._route_kontext_api_pipeline(user_text, has_images, image_count, query)
        else:
            # 未知管道类型
            result = _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.AIGEN_TEXT_ONLY,
                confidence=RoutingConfidence.UNKNOWN,
                reasoning="未知管道类型，使用默认工作流",
                fallback_used=True,
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="flux_default.json"
            )
        
        return result

    async def _get_optimized_prompt(self, user_text: str, query: Optional[Any]) -> str:
        """获取LLM优化后的提示词"""
        try:
            self.logger.info(f"🔧 开始获取优化提示词...")

            # 调用参数分析获取LLM优化后的提示词
            param_result = await self.analyze_parameters(user_text, query)

            if param_result.success and param_result.parameters:
                optimized_prompt = param_result.parameters.get('prompt', '')
                if optimized_prompt and optimized_prompt.strip():
                    self.logger.info(f"✅ 参数分析成功，优化提示词: {optimized_prompt}")
                    return optimized_prompt

            # 参数分析失败，返回原始文本
            self.logger.warning("⚠️ LLM参数分析失败，返回原始提示词")
            return user_text.replace("aigen ", "").strip() if user_text.startswith("aigen ") else user_text

        except Exception as e:
            self.logger.error(f"❌ 获取优化提示词异常: {e}")
            return user_text.replace("aigen ", "").strip() if user_text.startswith("aigen ") else user_text

    async def _route_aigen_pipeline(
        self,
        user_text: str,
        has_images: bool,
        image_count: int,
        query: Optional[Any] = None
    ) -> '_Level2RoutingResult':
        """AIGEN管道内工作流选择 - 完全基于LLM智能分析"""
        start_time = time.time()

        self.logger.info(f"🎨 AIGEN管道智能路由 - 文本: '{user_text}', 图片: {has_images}, 数量: {image_count}")

        # 使用LLM进行智能工作流选择
        llm_result = await self._route_aigen_with_llm(user_text, has_images, image_count, query)

        if llm_result:
            # LLM分析成功
            llm_result.processing_time_ms = (time.time() - start_time) * 1000
            self.logger.info(f"✅ LLM路由成功: {llm_result.workflow_subtype.value} (置信度: {llm_result.confidence.value})")
            return llm_result

        # LLM分析失败，诚实告知用户
        self.logger.warning("⚠️ LLM分析失败，诚实告知用户")
        optimized_prompt = await self._get_optimized_prompt(user_text, query)

        # 根据图片数量选择默认工作流
        if not has_images or image_count == 0:
            default_subtype = WorkflowSubType.AIGEN_TEXT_ONLY
            default_file = "flux_default.json"
            clarification = "LLM分析暂时不可用。纯文生图工作流已选择。"
        elif image_count == 1:
            default_subtype = WorkflowSubType.AIGEN_REFERENCE_ONLY
            default_file = "flux_redux.json"
            clarification = "LLM分析暂时不可用。请明确指定这张图片是用于'控制结构/姿势'还是'风格参考'？"
        else:
            default_subtype = WorkflowSubType.AIGEN_CONTROL_REFERENCE
            default_file = "flux_controlnet_redux.json"
            clarification = "LLM分析暂时不可用。请明确指定每张图片的用途。"

        return _Level2RoutingResult(
            workflow_subtype=default_subtype,
            confidence=RoutingConfidence.LOW,
            reasoning="LLM暂时不可用，使用默认工作流",
            processing_time_ms=(time.time() - start_time) * 1000,
            suggested_prompt=optimized_prompt,
            fallback_used=True,
            needs_clarification=True,
            clarification_question=clarification,
            workflow_file=default_file
        )

    async def _route_aigen_with_llm(
        self,
        user_text: str,
        has_images: bool,
        image_count: int,
        query: Optional[Any] = None
    ) -> Optional['_Level2RoutingResult']:
        """使用LLM进行AIGEN工作流智能路由"""
        try:
            if not query:
                self.logger.warning("⚠️ 无LLM查询对象，无法进行智能路由")
                return None

            # 构建LLM分析提示
            analysis_prompt = self._build_aigen_analysis_prompt(user_text, has_images, image_count)

            self.logger.info(f"🤖 发送LLM分析请求...")

            # 调用LLM进行分析
            llm_response = await self._call_llm_for_routing(
                system_prompt=self._get_aigen_workflow_system_prompt(),
                user_prompt=analysis_prompt,
                query=query
            )

            if not llm_response:
                self.logger.warning("⚠️ LLM响应为空")
                return None

            # 解析LLM响应
            parsed_result = self._parse_aigen_llm_response(llm_response)

            if not parsed_result:
                self.logger.warning("⚠️ LLM响应解析失败")
                return None

            # 构建路由结果
            workflow_subtype = self._map_aigen_workflow_type(parsed_result.get('workflow_subtype'))
            confidence = self._map_confidence(parsed_result.get('confidence', 'medium'))

            # 获取优化后的提示词
            suggested_prompt = parsed_result.get('suggested_prompt', user_text)
            if not suggested_prompt or suggested_prompt.strip() == "":
                suggested_prompt = await self._basic_prompt_optimization(user_text)

            # 选择工作流文件
            workflow_file = self._get_aigen_workflow_file(workflow_subtype)

            self.logger.info(f"✅ LLM分析成功: {workflow_subtype.value} (置信度: {confidence.value})")

            return _Level2RoutingResult(
                workflow_subtype=workflow_subtype,
                confidence=confidence,
                reasoning=parsed_result.get('reasoning', 'LLM智能分析结果'),
                suggested_prompt=suggested_prompt,
                workflow_file=workflow_file,
                image_types=parsed_result.get('image_analysis', {})
            )

        except Exception as e:
            self.logger.error(f"LLM路由分析失败: {e}")
            return None













    def _route_kontext_pipeline(self, has_images: bool, image_count: int) -> '_Level2RoutingResult':
        """KONTEXT管道内工作流选择 - 基于具体图片数量精确判断"""
        start_time = time.time()

        if not has_images or image_count == 0:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_1IMAGE,
                confidence=RoutingConfidence.LOW,
                reasoning="KONTEXT工作流需要图片，但未提供",
                processing_time_ms=(time.time() - start_time) * 1000,
                needs_clarification=True,
                clarification_question="KONTEXT工作流需要至少一张图片，请上传图片",
                workflow_file="kontext_local_single_image.json"
            )

        if image_count == 1:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_1IMAGE,
                confidence=RoutingConfidence.HIGH,
                reasoning="1张图片，选择单图处理工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="kontext_local_single_image.json"
            )
        elif image_count == 2:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_2IMAGE,
                confidence=RoutingConfidence.HIGH,
                reasoning="2张图片，选择双图处理工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="kontext_local_double_images.json"
            )
        elif image_count == 3:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_3IMAGE,
                confidence=RoutingConfidence.HIGH,
                reasoning="3张图片，选择三图处理工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="kontext_local_triple_images.json"
            )
        else:
            # 超过3张图片，使用3图工作流并提示用户
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_3IMAGE,
                confidence=RoutingConfidence.MEDIUM,
                reasoning=f"提供了{image_count}张图片，KONTEXT最多支持3张，将使用前3张图片",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="kontext_local_triple_images.json",
                needs_clarification=True,
                clarification_question=f"您提供了{image_count}张图片，但KONTEXT工作流最多支持3张图片，将使用前3张图片进行处理。"
            )

    async def _route_kontext_api_pipeline(
        self,
        user_text: str,
        has_images: bool,
        image_count: int,
        query: Optional[Any] = None
    ) -> '_Level2RoutingResult':
        """KONTEXT_API管道内工作流选择 - 使用LLM分析"""
        start_time = time.time()

        if not has_images or image_count == 0:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_API_1IMAGE,
                confidence=RoutingConfidence.LOW,
                reasoning="KONTEXT_API工作流需要图片，但未提供",
                processing_time_ms=(time.time() - start_time) * 1000,
                needs_clarification=True,
                clarification_question="KONTEXT_API工作流需要至少一张图片，请上传图片",
                workflow_file="kontext_api_1image.json"
            )

        # 使用LLM分析用户意图选择具体工作流
        if query:
            llm_result = await self._analyze_kontext_api_workflow_with_llm(
                user_text, has_images, image_count, query
            )
            if llm_result:
                return llm_result

        # LLM分析失败时的回退逻辑 - 按具体图片数量精确路由
        self.logger.warning("LLM分析失败，使用基于图片数量的回退逻辑")

        if image_count == 1:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_API_1IMAGE,
                confidence=RoutingConfidence.MEDIUM,
                reasoning="LLM不可用，基于1张图片选择API单图处理工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                fallback_used=True,
                workflow_file="kontext_api_1image.json"
            )
        elif image_count == 2:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_API_2IMAGE,
                confidence=RoutingConfidence.MEDIUM,
                reasoning="LLM不可用，基于2张图片选择API双图处理工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                fallback_used=True,
                workflow_file="kontext_api_2images.json"
            )
        elif image_count == 3:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_API_3IMAGE,
                confidence=RoutingConfidence.MEDIUM,
                reasoning="LLM不可用，基于3张图片选择API三图处理工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                fallback_used=True,
                workflow_file="kontext_api_3images.json"
            )
        else:
            # 超过3张图片或其他情况，使用3图工作流
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_API_3IMAGE,
                confidence=RoutingConfidence.LOW,
                reasoning=f"LLM不可用，提供了{image_count}张图片，API最多支持3张，将使用前3张图片",
                processing_time_ms=(time.time() - start_time) * 1000,
                fallback_used=True,
                workflow_file="kontext_api_3images.json"
            )



    async def _analyze_kontext_api_workflow_with_llm(
        self,
        user_text: str,
        image_count: int,
        query: Any
    ) -> Optional['_Level2RoutingResult']:
        """使用LLM分析KONTEXT_API工作流选择"""
        try:
            system_prompt = """你是KONTEXT_API云端图像处理工作流的智能路由器。

KONTEXT_API工作流用于复杂的云端图像处理任务，根据图片数量选择对应的处理流程。

## 可用的工作流选项：

### 1. kontext_api_1image - 单图处理工作流
- **文件**: kontext_api_1image.json
- **适用于**: 单张图片的深度处理、专业分析、高质量增强
- **功能**: 图像修复、超分辨率、风格转换、专业调色

### 2. kontext_api_2image - 双图处理工作流
- **文件**: kontext_api_2images.json
- **适用于**: 两张图片的对比、融合、关联处理
- **功能**: 图片对比分析、风格迁移、图像融合、批量处理

### 3. kontext_api_3image - 三图处理工作流
- **文件**: kontext_api_3images.json
- **适用于**: 三张图片的复杂组合、序列处理、批量操作
- **功能**: 序列分析、多图融合、复杂变换、批量优化

## 路由示例：

**示例1 - 选择kontext_api_1image:**
用户: "对这张照片进行专业级的画质增强" + 1张图片
分析: 单图深度处理 → kontext_api_1image

**示例2 - 选择kontext_api_2image:**
用户: "把第一张图的风格应用到第二张图上" + 2张图片
分析: 双图风格迁移 → kontext_api_2image

**示例3 - 选择kontext_api_3image:**
用户: "对这三张图片进行统一的色彩校正" + 3张图片
分析: 三图批量处理 → kontext_api_3image

## 判断原则：
1. **图片数量**: 严格按照图片数量选择对应工作流
2. **处理复杂度**: 考虑任务的复杂程度和云端资源需求
3. **用户意图**: 理解用户想要的具体处理效果

返回JSON格式：
{
  "workflow_subtype": "kontext_api_1image|kontext_api_2image|kontext_api_3image",
  "confidence": "high|medium|low",
  "reasoning": "选择原因",
  "suggested_prompt": "优化后的处理指令"
}
"""

            user_prompt = f"""用户输入: "{user_text}"
图片数量: {image_count}

请分析用户的云端图像处理需求并选择合适的KONTEXT_API工作流。"""

            response_text = await self._call_llm(query, system_prompt, user_prompt)

            if response_text:
                try:
                    cleaned_text = self._clean_json_response(response_text)
                    llm_result = json.loads(cleaned_text)

                    # 解析结果 - 按具体图片数量映射
                    subtype_str = llm_result.get('workflow_subtype', 'kontext_api_1image')
                    if subtype_str == 'kontext_api_1image':
                        workflow_subtype = WorkflowSubType.KONTEXT_API_1IMAGE
                        workflow_file = "kontext_api_1image.json"
                    elif subtype_str == 'kontext_api_2image':
                        workflow_subtype = WorkflowSubType.KONTEXT_API_2IMAGE
                        workflow_file = "kontext_api_2images.json"
                    elif subtype_str == 'kontext_api_3image':
                        workflow_subtype = WorkflowSubType.KONTEXT_API_3IMAGE
                        workflow_file = "kontext_api_3images.json"
                    else:
                        # 默认使用1图工作流
                        workflow_subtype = WorkflowSubType.KONTEXT_API_1IMAGE
                        workflow_file = "kontext_api_1image.json"

                    confidence_str = llm_result.get('confidence', 'medium')
                    confidence_map = {
                        'high': RoutingConfidence.HIGH,
                        'medium': RoutingConfidence.MEDIUM,
                        'low': RoutingConfidence.LOW
                    }
                    confidence = confidence_map.get(confidence_str, RoutingConfidence.MEDIUM)

                    return _Level2RoutingResult(
                        workflow_subtype=workflow_subtype,
                        confidence=confidence,
                        reasoning=f"LLM分析: {llm_result.get('reasoning', '')}",
                        processing_time_ms=0,  # 会在调用方计算
                        suggested_prompt=llm_result.get('suggested_prompt', ''),
                        workflow_file=workflow_file
                    )

                except json.JSONDecodeError as e:
                    self.logger.error(f"KONTEXT_API LLM返回JSON格式错误: {e}")
                    return None

        except Exception as e:
            self.logger.error(f"KONTEXT_API LLM分析失败: {e}")
            return None







    def get_routing_stats(self) -> Dict[str, Any]:
        """获取路由统计信息"""
        return {
            "level_1_keywords": list(self.level_1_keywords.keys()),
            "workflow_files": dict(self.workflow_files),
            "config": {
                "level_2_enabled": self.level_2_config["enabled"],
                "timeout_ms": self.level_2_config["timeout_ms"]
            }
        }


def get_unified_router(ap=None) -> UnifiedRoutingSystem:
    """获取统一路由器实例"""
    return UnifiedRoutingSystem(ap) 